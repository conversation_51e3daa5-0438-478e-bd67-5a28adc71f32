{"scripts": {"lint": "./node_modules/.bin/eslint", "check-lint": "lint [0-9]*.js", "dev": "npx babel-node", "test": "jest", "full-test": "./node_modules/.bin/eslint [0-9]*.js && jest"}, "devDependencies": {"@babel/core": "^7.6.0", "@babel/node": "^7.8.0", "@babel/preset-env": "^7.6.0", "eslint": "^9.33.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jest": "^29.0.1", "jest": "^30.0.5"}}