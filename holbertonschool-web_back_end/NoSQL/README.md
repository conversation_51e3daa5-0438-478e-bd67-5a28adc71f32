# MongoDB Python Scripts

This project contains Python scripts for interacting with MongoDB using **PyMongo**.

## Environment
- Ubuntu 20.04 LTS
- Python 3.9
- PyMongo 4.8.0
- MongoDB 4.4

## Installation

1. **Install MongoDB 4.4**
   Follow the [official MongoDB installation guide](https://www.mongodb.com/docs/v4.4/tutorial/install-mongodb-on-ubuntu/).

2. **Install PyMongo**
   ```bash
   pip install pymongo==4.8.0

