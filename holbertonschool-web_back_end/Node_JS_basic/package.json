{"name": "node_js_basics", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"lint": "./node_modules/.bin/eslint", "check-lint": "lint [0-9]*.js", "test": "./node_modules/mocha/bin/mocha --require babel-register --exit", "dev": "nodemon --exec babel-node --presets babel-preset-env ./server.js ./database.csv"}, "author": "", "license": "ISC", "dependencies": {"chai-http": "^4.3.0", "express": "^4.17.1"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "lint": "*", "eslint": "^6.8.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^22.21.0", "nodemon": "^2.0.22", "chai": "^4.4.1", "mocha": "^6.2.3", "request": "^2.88.2", "sinon": "^7.5.0"}}